#!/usr/bin/env python3
"""
测试定期转账检测性能优化
验证大数据集处理性能和响应性
"""

import sys
import os
import tempfile
import pandas as pd
import time
import random

# 添加当前目录到路径
sys.path.insert(0, '.')

def generate_large_test_data(num_customers=100, transactions_per_customer=50):
    """生成大量测试数据"""
    print(f"🔄 生成测试数据: {num_customers} 个客户，每个客户 {transactions_per_customer} 笔交易...")
    
    data = []
    
    for customer_id in range(1, num_customers + 1):
        customer_code = f"CUST{customer_id:06d}"
        
        # 为每个客户生成随机交易
        for i in range(transactions_per_customer):
            # 随机生成日期（2024年）
            month = random.randint(1, 12)
            day = random.randint(1, 28)
            date_str = f"2024{month:02d}{day:02d}"
            
            # 随机生成金额（包括一些大额和小额）
            if random.random() < 0.3:  # 30%的概率是大额交易
                amount = random.randint(10000, 100000)
            else:  # 70%的概率是小额交易
                amount = random.randint(100, 9999)
            
            data.append({
                '客户编号': customer_code,
                '发生金额': str(amount),
                '交易日期': date_str,
                '银行错误信': '交易成功'
            })
    
    # 添加一些明确的定期转账模式
    for i in range(min(10, num_customers // 10)):  # 添加一些定期转账客户
        customer_code = f"REGULAR{i:03d}"
        
        # 连续三个月的定期转账
        for month in [1, 2, 3]:
            data.append({
                '客户编号': customer_code,
                '发生金额': '15000',
                '交易日期': f"2024{month:02d}15",
                '银行错误信': '交易成功'
            })
    
    print(f"✅ 生成完成，总计 {len(data)} 条记录")
    return data

def test_performance_small_dataset():
    """测试小数据集性能"""
    print("🧪 测试小数据集性能...")
    
    from merge_dbf import RiskAnalyzer
    
    # 生成小数据集
    test_data = generate_large_test_data(num_customers=10, transactions_per_customer=20)
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        analyzer = RiskAnalyzer(temp_file.name)
        analyzer._load_data()
        
        # 测试执行时间
        start_time = time.time()
        regular_risks = analyzer._check_regular_transfer_risk()
        end_time = time.time()
        
        execution_time = end_time - start_time
        print(f"📊 小数据集执行时间: {execution_time:.2f} 秒")
        print(f"📊 检测到的风险记录: {len(regular_risks)} 条")
        
        # 小数据集应该在1秒内完成
        if execution_time < 1.0:
            print("✅ 小数据集性能测试通过")
            return True
        else:
            print("❌ 小数据集性能测试失败（耗时过长）")
            return False
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_performance_medium_dataset():
    """测试中等数据集性能"""
    print("🧪 测试中等数据集性能...")
    
    from merge_dbf import RiskAnalyzer
    
    # 生成中等数据集
    test_data = generate_large_test_data(num_customers=50, transactions_per_customer=100)
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        analyzer = RiskAnalyzer(temp_file.name)
        analyzer._load_data()
        
        # 测试执行时间
        start_time = time.time()
        regular_risks = analyzer._check_regular_transfer_risk()
        end_time = time.time()
        
        execution_time = end_time - start_time
        print(f"📊 中等数据集执行时间: {execution_time:.2f} 秒")
        print(f"📊 检测到的风险记录: {len(regular_risks)} 条")
        
        # 中等数据集应该在10秒内完成
        if execution_time < 10.0:
            print("✅ 中等数据集性能测试通过")
            return True
        else:
            print("❌ 中等数据集性能测试失败（耗时过长）")
            return False
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_performance_large_dataset():
    """测试大数据集性能"""
    print("🧪 测试大数据集性能...")
    
    from merge_dbf import RiskAnalyzer
    
    # 生成大数据集
    test_data = generate_large_test_data(num_customers=100, transactions_per_customer=200)
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        analyzer = RiskAnalyzer(temp_file.name)
        analyzer._load_data()
        
        # 测试执行时间
        start_time = time.time()
        regular_risks = analyzer._check_regular_transfer_risk()
        end_time = time.time()
        
        execution_time = end_time - start_time
        print(f"📊 大数据集执行时间: {execution_time:.2f} 秒")
        print(f"📊 检测到的风险记录: {len(regular_risks)} 条")
        
        # 大数据集应该在30秒内完成
        if execution_time < 30.0:
            print("✅ 大数据集性能测试通过")
            return True
        else:
            print("❌ 大数据集性能测试失败（耗时过长）")
            return False
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_memory_usage():
    """测试内存使用情况"""
    print("🧪 测试内存使用情况...")
    
    import psutil
    import gc
    
    from merge_dbf import RiskAnalyzer
    
    # 记录初始内存使用
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    # 生成测试数据
    test_data = generate_large_test_data(num_customers=50, transactions_per_customer=100)
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        analyzer = RiskAnalyzer(temp_file.name)
        analyzer._load_data()
        
        # 记录加载后内存使用
        after_load_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行风险检测
        regular_risks = analyzer._check_regular_transfer_risk()
        
        # 记录处理后内存使用
        after_process_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 清理
        del analyzer
        del test_data
        gc.collect()
        
        # 记录清理后内存使用
        after_cleanup_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"📊 内存使用情况:")
        print(f"   - 初始内存: {initial_memory:.1f} MB")
        print(f"   - 加载后内存: {after_load_memory:.1f} MB")
        print(f"   - 处理后内存: {after_process_memory:.1f} MB")
        print(f"   - 清理后内存: {after_cleanup_memory:.1f} MB")
        
        # 检查内存增长是否合理
        memory_growth = after_process_memory - initial_memory
        if memory_growth < 500:  # 内存增长应该小于500MB
            print("✅ 内存使用测试通过")
            return True
        else:
            print("❌ 内存使用测试失败（内存增长过多）")
            return False
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始定期转账检测性能优化测试...")
    print("=" * 60)
    
    tests = [
        test_performance_small_dataset,
        test_performance_medium_dataset,
        test_performance_large_dataset,
        test_memory_usage
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print("✅ 测试通过\n")
            else:
                print("❌ 测试失败\n")
        except Exception as e:
            print(f"❌ 测试出错: {str(e)}\n")
    
    print("=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 性能优化测试全部通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
