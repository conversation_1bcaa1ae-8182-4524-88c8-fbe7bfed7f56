# DBF数据处理脚本 - 风险排查功能使用指南（7项改进版）

## 🚀 功能概览

本次更新对DBF数据处理脚本的风险排查功能进行了7项重要改进：

### 1. IP同源数据文件选择功能增强 ✅
- **多文件选择支持**：从单选改为多选，可同时选择多个IP同源数据文件
- **独立文件处理**：每个选中的文件单独进行IP归属地查询，生成独立结果文件
- **移除自动探测**：不再自动查找IP文件，强制要求用户手动选择
- **适用于所有模式**：完整处理模式和风险排查模式均支持

### 2. IP地址解析逻辑优化 ✅
- **单引号处理**：智能删除IP地址开头的单引号"'"符号
- **12位数字串解析**：支持"'100127008112"格式，解析为"*************"
  - 按1-3位、4-6位、7-9位、10-12位分组
  - 自动去除前导零（如"009"→"9"）
  - 用"."连接各组形成IPv4地址
- **容错处理增强**：对解析失败的数据正确跳过并记录详细日志
- **列检测优化**：更准确识别包含IP数据的"重复值"列

### 3. 风险排查结果文件格式调整 ✅
- **风险信息列前置**：将"风险信息"列移动到结果文件的第一列位置，便于查看

### 4. 整数转账风险规则调整 ✅
- **移除"01"模式**：不再检测包含"01"的数字模式
- **保留其他模式**：继续检测999、998、001、111、222、333、444、555、666、777、888

### 5. 风险排查文件命名规范 ✅
- **新命名格式**：原文件名 + "_风险信息排查" + 扩展名
- **示例**：data.csv → data_风险信息排查.csv

### 6. 新增"资金定期定额转出"风险规则 ✅
实现复杂的定期转账模式识别：
- **检测逻辑**：同一客户编号在连续三个月的相同日期（±1天容差）进行相似金额转账
- **时间匹配**：连续三个月（如01、02、03月或09、10、11月）
- **日期容差**：目标日期前后一天内的交易都视为匹配
- **金额容差**：相同金额或上下浮动10000元内的交易视为匹配
- **标记内容**："存在风险-资金定期定额转出"
- **示例**：客户12345678在20240501、20240602、20240701都有55000元交易，三条记录都标记此风险

### 7. 界面文本清理 ✅
- **删除"PyQt5增强版"表述**：清理程序标题、窗口标题、日志信息等所有位置的相关表述
- **保持专业性**：维护功能描述的专业性和简洁性

## 📋 完整风险识别规则

### 风险模式详细说明：
1. **整数或近似整数转账**
   - 发生金额 = 10000的整数倍
   - 发生金额 > 10000且包含999、998、001、111-888等模式（已移除"01"模式）
   - 标记："存在风险-整数或近似整数转账"

2. **发薪日临近交易**
   - 发生金额 ≥ 10000 且交易日期为09-17日
   - 标记："存在风险-临近发薪日交易"

3. **月初月末交易**
   - 发生金额 ≥ 10000 且交易日期为01-05日或26-31日
   - 标记："存在风险-月初月末交易"

4. **试密过程异常**
   - 同一客户同一日期的银行错误信中同时包含"交易成功"/"转账成功"和"密码"关键词
   - 标记："疑似他人操作-试密过程异常"

5. **频繁操作**
   - 同一客户同一日期的记录数 > 15条
   - 标记："存在风险-单日内频繁操作"

6. **资金定期定额转出**（新增）
   - 同一客户连续三个月相同日期（±1天）相似金额（±10000元）转账
   - 标记："存在风险-资金定期定额转出"

## 🖥️ GUI界面使用说明

### 启动应用
```bash
python merge_dbf.py
```

### 处理模式选择

#### 1. 完整处理模式 🔄
- **功能**：执行DBF文件合并、地址解析、同源数据智能识别和风险排查的完整流程
- **文件选择**：
  - 选择DBF文件（多选）
  - 选择输出路径
- **执行顺序**：合并 → 解析 → 同源分析 → 风险排查

#### 2. 跳转分析模式 ⚡
- **功能**：跳过DBF合并步骤，直接对已处理文件进行同源数据智能识别
- **文件选择**：选择已处理的CSV或Parquet文件
- **适用场景**：重新分析已合并的数据

#### 3. 风险排查模式 🔍（新增）
- **功能**：基于智能识别同源数据分析结果进行风险排查
- **文件选择**：
  - 🌐 选择IP同源数据文件（Excel或CSV格式）
  - ⚠️ 选择DBF合并文件（CSV或Parquet格式）
- **处理内容**：
  - IP归属地查询（如果提供IP同源数据文件）
  - 风险信息识别（如果提供DBF合并文件）

## 📋 使用示例

### 示例1：完整处理流程
1. 启动程序：`python merge_dbf.py`
2. 选择"🔄 完整处理模式"
3. 选择多个DBF文件
4. 指定输出路径（如：`merged_data.csv`）
5. 点击"🚀 开始处理"
6. 系统自动执行：合并 → 解析 → 同源分析 → 风险排查

### 示例2：单独风险排查
1. 启动程序：`python merge_dbf.py`
2. 选择"🔍 风险排查模式"
3. 选择IP同源数据文件（可选）
4. 选择DBF合并文件（必选）
5. 点击"🚀 开始处理"
6. 查看处理日志和进度

## 📊 输出文件说明

### IP归属地查询结果
- **文件名**：原文件名 + "（带IP归属地）" + 原扩展名
- **新增列**：在每个IP重复值列后添加"IP地址归属地"列
- **内容**：显示IP地址对应的地理位置信息

### 风险信息识别结果
- **文件名**：原文件名 + "（风险信息排查）" + 原扩展名
- **新增列**："风险信息"列
- **内容**：只保留有风险信息的记录，包含原有所有列plus风险信息列

## ⚠️ 注意事项

### API使用限制
- IP归属地查询使用第三方API：https://www.36ip.cn/
- 查询间隔：2-5秒
- 频率限制：每分钟最多45次查询
- 建议在网络稳定环境下使用

### 文件格式要求
- **IP同源数据文件**：Excel(.xlsx)或CSV(.csv)格式
- **DBF合并文件**：CSV(.csv)或Parquet(.parquet)格式
- **必要列**：风险分析需要"发生金额"、"交易日期"、"客户编号"、"银行错误信"列

### 性能建议
- 大量IP查询可能耗时较长，请耐心等待
- 建议在处理大文件时关闭其他占用内存的程序
- 查看处理日志了解详细进度

## 🔧 故障排除

### 常见问题
1. **IP查询失败**：检查网络连接，API可能暂时不可用
2. **文件格式错误**：确保文件格式符合要求
3. **缺少必要列**：检查数据文件是否包含所需列

### 错误处理
- 程序具有完善的错误处理机制
- 查看处理日志获取详细错误信息
- IP查询失败不会影响风险信息识别

## 📈 技术特性

### 性能优化
- **内存监控**：实时监控内存使用情况
- **分块处理**：大文件采用分块处理策略
- **智能缓存**：IP查询结果缓存，避免重复查询

### 用户体验
- **实时进度**：详细的进度条和状态显示
- **集成日志**：所有处理信息在GUI中实时显示
- **智能提示**：清晰的操作指导和状态反馈

## 🎯 版本信息

- **版本**：风险排查功能增强版
- **更新日期**：2025-06-20
- **兼容性**：完全向后兼容，保持原有功能不变
- **依赖**：新增 requests、ipaddress 库依赖

---

🎉 **恭喜！** 您现在可以使用全新的风险排查功能来识别潜在的金融风险模式！
