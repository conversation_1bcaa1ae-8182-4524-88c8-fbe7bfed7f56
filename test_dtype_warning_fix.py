#!/usr/bin/env python3
"""
测试DtypeWarning修复
验证所有pd.read_csv调用都正确处理了dtype警告
"""

import sys
import os
import tempfile
import pandas as pd
import warnings
import io

# 添加当前目录到路径
sys.path.insert(0, '.')

def test_csv_reading_no_warnings():
    """测试CSV读取不产生DtypeWarning"""
    print("🧪 测试CSV读取DtypeWarning修复...")
    
    # 创建包含混合数据类型的测试CSV文件
    test_data = {
        'col1': ['text1', 'text2', 'text3'],
        'col2': [1, 2, 3],
        'col3': [1.1, 2.2, 3.3],
        'col4': ['2023-01-01', '2023-01-02', '2023-01-03'],
        'col5': [True, False, True],
        'col6': ['mixed', 123, 45.6],  # 混合类型列
        'col7': ['', 'NA', 'null'],  # 包含NA值
    }
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        # 测试IPLocationQuerier的CSV读取
        from merge_dbf import IPLocationQuerier
        
        # 捕获警告
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            querier = IPLocationQuerier(temp_file.name)
            querier._load_data()
            
            # 检查是否有DtypeWarning
            dtype_warnings = [warning for warning in w if 'DtypeWarning' in str(warning.category)]
            
            if dtype_warnings:
                print(f"❌ IPLocationQuerier产生了DtypeWarning: {len(dtype_warnings)} 个")
                for warning in dtype_warnings:
                    print(f"   警告: {warning.message}")
                return False
            else:
                print("✅ IPLocationQuerier没有产生DtypeWarning")
        
        # 测试RiskAnalyzer的CSV读取
        from merge_dbf import RiskAnalyzer
        
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            analyzer = RiskAnalyzer(temp_file.name)
            analyzer._load_data()
            
            # 检查是否有DtypeWarning
            dtype_warnings = [warning for warning in w if 'DtypeWarning' in str(warning.category)]
            
            if dtype_warnings:
                print(f"❌ RiskAnalyzer产生了DtypeWarning: {len(dtype_warnings)} 个")
                for warning in dtype_warnings:
                    print(f"   警告: {warning.message}")
                return False
            else:
                print("✅ RiskAnalyzer没有产生DtypeWarning")
        
        return True
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_dbf_merger_csv_reading():
    """测试DBFMergerPro的CSV读取"""
    print("🧪 测试DBFMergerPro CSV读取...")
    
    # 创建包含混合数据类型的测试CSV文件
    test_data = {
        'col1': ['text1', 'text2', 'text3'],
        'col2': [1, 2, 3],
        'col3': [1.1, 2.2, 3.3],
        'col4': ['mixed', 123, 45.6],  # 混合类型列
    }
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        from merge_dbf import DBFMergerPro
        
        # 捕获警告
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            merger = DBFMergerPro()
            merger.source_file = temp_file.name
            
            # 测试load_data方法
            try:
                merger.load_data()
                
                # 检查是否有DtypeWarning
                dtype_warnings = [warning for warning in w if 'DtypeWarning' in str(warning.category)]
                
                if dtype_warnings:
                    print(f"❌ DBFMergerPro产生了DtypeWarning: {len(dtype_warnings)} 个")
                    for warning in dtype_warnings:
                        print(f"   警告: {warning.message}")
                    return False
                else:
                    print("✅ DBFMergerPro没有产生DtypeWarning")
                    
            except Exception as e:
                print(f"⚠️ DBFMergerPro测试跳过（方法可能不存在）: {str(e)}")
        
        return True
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_pandas_read_csv_directly():
    """直接测试pandas读取CSV的警告情况"""
    print("🧪 测试pandas直接读取CSV...")
    
    # 创建包含混合数据类型的测试CSV文件
    test_data = {
        'col1': ['text1', 'text2', 'text3'] * 100,  # 增加数据量
        'col2': [1, 2, 3] * 100,
        'col3': [1.1, 2.2, 3.3] * 100,
        'col4': ['mixed', 123, 45.6] * 100,  # 混合类型列
        'col5': ['', 'NA', 'null'] * 100,  # 包含NA值
    }
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        # 测试不同的读取方式
        test_cases = [
            ("默认读取", {}),
            ("low_memory=False", {"low_memory": False}),
            ("dtype=str", {"dtype": str}),
            ("low_memory=False + dtype=str", {"low_memory": False, "dtype": str}),
        ]
        
        for name, kwargs in test_cases:
            with warnings.catch_warnings(record=True) as w:
                warnings.simplefilter("always")
                
                df = pd.read_csv(temp_file.name, encoding='utf-8-sig', **kwargs)
                
                # 检查是否有DtypeWarning
                dtype_warnings = [warning for warning in w if 'DtypeWarning' in str(warning.category)]
                
                if dtype_warnings:
                    print(f"❌ {name}: 产生了DtypeWarning ({len(dtype_warnings)} 个)")
                else:
                    print(f"✅ {name}: 没有产生DtypeWarning")
        
        return True
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始DtypeWarning修复测试...")
    print("=" * 60)
    
    tests = [
        test_csv_reading_no_warnings,
        test_dbf_merger_csv_reading,
        test_pandas_read_csv_directly
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print("✅ 测试通过\n")
            else:
                print("❌ 测试失败\n")
        except Exception as e:
            print(f"❌ 测试出错: {str(e)}\n")
    
    print("=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 DtypeWarning修复测试全部通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
