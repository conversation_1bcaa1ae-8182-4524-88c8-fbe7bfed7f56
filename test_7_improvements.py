#!/usr/bin/env python3
"""
测试DBF风险排查功能7项改进
验证所有改进功能的正确性
"""

import sys
import os
import tempfile
import pandas as pd
import unittest
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.insert(0, '.')

def test_ip_address_parsing():
    """测试IP地址解析逻辑优化"""
    print("🧪 测试1: IP地址解析逻辑优化...")
    
    from merge_dbf import IPLocationQuerier
    
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    temp_file.write("IIP重复值\n'***********\n'100127008112\n'********\ninvalid_ip\n")
    temp_file.close()
    
    try:
        querier = IPLocationQuerier(temp_file.name)
        
        # 测试IP地址清理功能
        test_cases = [
            ("'***********", "***********"),  # 单引号处理
            ("'100127008112", "*************"),  # 12位数字串解析
            ("'********", "********"),  # 标准IP
            ("invalid_ip", ""),  # 无效IP
            ("'999999999999", ""),  # 无效12位数字（超出范围）
        ]
        
        all_passed = True
        for input_ip, expected in test_cases:
            result = querier._clean_ip_address(input_ip)
            if result != expected:
                print(f"   ❌ IP解析失败: '{input_ip}' 期望 '{expected}', 实际 '{result}'")
                all_passed = False
            else:
                print(f"   ✅ IP解析正确: '{input_ip}' -> '{result}'")
        
        # 测试IPv4验证
        ipv4_test_cases = [
            ("*************", True),
            ("***********", True),
            ("256.1.1.1", False),  # 超出范围
            ("1.2.3", False),  # 格式错误
        ]
        
        for ip, expected in ipv4_test_cases:
            result = querier._is_valid_ipv4(ip)
            if result != expected:
                print(f"   ❌ IPv4验证失败: '{ip}' 期望 {expected}, 实际 {result}")
                all_passed = False
        
        return all_passed
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_risk_analysis_improvements():
    """测试风险分析改进"""
    print("🧪 测试2: 风险分析改进...")
    
    from merge_dbf import RiskAnalyzer
    
    # 创建测试数据
    test_data = {
        '客户编号': ['CUST001', 'CUST002', 'CUST003', 'CUST001', 'CUST001'],
        '发生金额': ['10000', '15001', '8000', '55000', '55000'],  # 移除01模式测试
        '交易日期': ['20240501', '20240210', '20240205', '20240602', '20240701'],
        '银行错误信': ['交易成功', '密码错误', '余额不足', '交易成功', '转账成功']
    }
    
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        analyzer = RiskAnalyzer(temp_file.name)
        analyzer._load_data()
        
        # 测试整数转账风险（应该不包含01模式，但15001包含001仍会被检测）
        test_row_01_only = {'发生金额': '15010'}  # 只包含01，不包含001等其他模式
        result_01_only = analyzer._check_integer_transfer_risk(test_row_01_only)
        if result_01_only:
            print("   ❌ 01模式仍被检测（应该已移除）")
            return False
        else:
            print("   ✅ 01模式已正确移除")

        # 测试包含001的情况（应该仍被检测）
        test_row_001 = {'发生金额': '15001'}  # 包含001，应该被标记
        result_001 = analyzer._check_integer_transfer_risk(test_row_001)
        if not result_001:
            print("   ❌ 001模式检测失败")
            return False
        else:
            print("   ✅ 001模式检测正常")
        
        # 测试其他模式仍然有效
        test_row_999 = {'发生金额': '15999'}  # 包含999
        result_999 = analyzer._check_integer_transfer_risk(test_row_999)
        if not result_999:
            print("   ❌ 999模式检测失败")
            return False
        else:
            print("   ✅ 999模式检测正常")
        
        # 执行完整分析
        analyzer._analyze_risks()
        
        # 检查风险信息列是否存在
        if '风险信息' not in analyzer.data.columns:
            print("   ❌ 风险信息列未创建")
            return False
        
        # 检查定期转账风险检测
        regular_risks = analyzer._check_regular_transfer_risk()
        if len(regular_risks) > 0:
            print(f"   ✅ 检测到定期转账风险: {len(regular_risks)} 条")
        else:
            print("   ⚠️ 未检测到定期转账风险（可能是正常的）")
        
        # 测试文件保存和格式
        result_file = analyzer._save_risk_result()
        if result_file:
            # 检查文件名格式
            if "_风险信息排查" in result_file:
                print("   ✅ 文件命名格式正确")
            else:
                print("   ❌ 文件命名格式错误")
                return False
            
            # 检查风险信息列是否在第一列
            result_data = pd.read_csv(result_file, encoding='utf-8-sig')
            if result_data.columns[0] == '风险信息':
                print("   ✅ 风险信息列已移动到第一列")
            else:
                print(f"   ❌ 风险信息列位置错误，第一列是: {result_data.columns[0]}")
                return False
            
            # 清理测试文件
            try:
                os.unlink(result_file)
            except:
                pass
        
        return True
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_regular_transfer_detection():
    """测试定期转账检测功能"""
    print("🧪 测试3: 定期转账检测功能...")
    
    from merge_dbf import RiskAnalyzer
    
    # 创建包含定期转账模式的测试数据
    test_data = {
        '客户编号': ['CUST001'] * 6 + ['CUST002'] * 3,
        '发生金额': ['55000', '55000', '55000', '30000', '30000', '30000', '20000', '25000', '30000'],
        '交易日期': [
            '20240501', '20240602', '20240701',  # CUST001定期转账
            '20240515', '20240615', '20240715',  # CUST001另一组定期转账
            '20240510', '20240610', '20240710'   # CUST002定期转账
        ],
        '银行错误信': ['交易成功'] * 9
    }
    
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        analyzer = RiskAnalyzer(temp_file.name)
        analyzer._load_data()
        
        # 测试定期转账检测
        regular_risks = analyzer._check_regular_transfer_risk()
        
        if len(regular_risks) >= 3:  # 至少应该检测到一组定期转账（3条记录）
            print(f"   ✅ 检测到定期转账风险: {len(regular_risks)} 条记录")
            
            # 执行完整分析
            analyzer._analyze_risks()
            
            # 检查是否正确标记
            risk_records = analyzer.data[analyzer.data['风险信息'].str.contains('资金定期定额转出', na=False)]
            if len(risk_records) > 0:
                print(f"   ✅ 定期转账风险正确标记: {len(risk_records)} 条")
                return True
            else:
                print("   ❌ 定期转账风险未正确标记")
                return False
        else:
            print(f"   ❌ 定期转账检测失败，仅检测到 {len(regular_risks)} 条")
            return False
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_multi_file_selection():
    """测试多文件选择功能（模拟）"""
    print("🧪 测试4: 多文件选择功能...")
    
    # 由于GUI测试较复杂，这里主要测试逻辑
    from merge_dbf import IPLocationQuerier
    
    # 创建多个测试文件
    files = []
    for i in range(3):
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
        temp_file.write(f"IIP重复值\n192.168.1.{i+1}\n10.0.0.{i+1}\n")
        temp_file.close()
        files.append(temp_file.name)
    
    try:
        # 测试每个文件单独处理
        all_passed = True
        for i, file_path in enumerate(files):
            querier = IPLocationQuerier(file_path)
            querier._load_data()
            querier._extract_unique_ips()
            
            if len(querier.unique_ips) >= 1:
                print(f"   ✅ 文件 {i+1} 处理正常，提取到 {len(querier.unique_ips)} 个IP")
            else:
                print(f"   ❌ 文件 {i+1} 处理失败")
                all_passed = False
        
        return all_passed
        
    finally:
        for file_path in files:
            try:
                os.unlink(file_path)
            except:
                pass

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始DBF风险排查功能7项改进测试...")
    print("=" * 60)
    
    tests = [
        test_ip_address_parsing,
        test_risk_analysis_improvements,
        test_regular_transfer_detection,
        test_multi_file_selection
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print("✅ 测试通过\n")
            else:
                print("❌ 测试失败\n")
        except Exception as e:
            print(f"❌ 测试出错: {str(e)}\n")
    
    print("=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有改进功能测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
