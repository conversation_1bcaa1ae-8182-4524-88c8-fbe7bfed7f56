#!/usr/bin/env python3
"""
测试定期转账检测误报修复
验证新的金额要求（所有三笔交易都≥10000元）
"""

import sys
import os
import tempfile
import pandas as pd

# 添加当前目录到路径
sys.path.insert(0, '.')

def test_regular_transfer_false_positive_reduction():
    """测试定期转账误报减少"""
    print("🧪 测试定期转账误报减少...")
    
    from merge_dbf import RiskAnalyzer
    
    # 创建包含不同金额模式的测试数据
    test_data = {
        '客户编号': ['CUST001'] * 9 + ['CUST002'] * 6 + ['CUST003'] * 6,
        '发生金额': [
            # CUST001: 应该被检测（所有金额都≥10000）
            '15000', '15000', '15000',  # 连续三个月，金额≥10000
            '20000', '20000', '20000',  # 另一组连续三个月，金额≥10000
            '12000', '12000', '12000',  # 第三组连续三个月，金额≥10000
            
            # CUST002: 不应该被检测（有金额<10000）
            '5000', '15000', '15000',   # 第一笔<10000，应该被过滤
            '8000', '8000', '8000',     # 所有金额都<10000，应该被过滤
            
            # CUST003: 应该被检测（所有金额都≥10000）
            '10000', '10000', '10000',  # 边界情况：正好10000
            '11000', '12000', '13000',  # 金额在容差范围内且都≥10000
        ],
        '交易日期': [
            # CUST001: 连续三个月的相同日期
            '20240501', '20240602', '20240701',  # 第一组
            '20240515', '20240615', '20240715',  # 第二组
            '20240510', '20240610', '20240710',  # 第三组
            
            # CUST002: 连续三个月的相同日期
            '20240501', '20240602', '20240701',  # 第一组（但有小额）
            '20240515', '20240615', '20240715',  # 第二组（全部小额）
            
            # CUST003: 连续三个月的相同日期
            '20240501', '20240602', '20240701',  # 第一组（边界金额）
            '20240510', '20240610', '20240710',  # 第二组（容差范围）
        ],
        '银行错误信': ['交易成功'] * 21
    }
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        analyzer = RiskAnalyzer(temp_file.name)
        analyzer._load_data()
        
        # 执行定期转账风险检测
        regular_risks = analyzer._check_regular_transfer_risk()
        
        print(f"📊 检测到的定期转账风险记录数: {len(regular_risks)}")
        
        # 分析检测结果
        detected_customers = set()
        for idx, pattern in regular_risks.items():
            customer_id = analyzer.data.loc[idx, '客户编号']
            detected_customers.add(customer_id)
            amounts = pattern['amounts']
            print(f"   检测到: 客户{customer_id}, 金额{amounts}")
        
        print(f"📋 检测到的客户: {detected_customers}")
        
        # 验证结果
        expected_customers = {'CUST001', 'CUST003'}  # 只有这两个客户应该被检测
        unexpected_customers = {'CUST002'}  # 这个客户不应该被检测
        
        success = True
        
        # 检查是否正确检测了预期客户
        missing_customers = expected_customers - detected_customers
        if missing_customers:
            print(f"❌ 未检测到预期客户: {missing_customers}")
            success = False
        
        # 检查是否错误检测了不应该检测的客户
        false_positive_customers = detected_customers & unexpected_customers
        if false_positive_customers:
            print(f"❌ 错误检测了客户（误报）: {false_positive_customers}")
            success = False
        
        if success:
            print("✅ 定期转账误报减少测试通过")
        
        return success
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_amounts_match_logic():
    """测试金额匹配逻辑"""
    print("🧪 测试金额匹配逻辑...")
    
    from merge_dbf import RiskAnalyzer
    
    # 创建临时文件用于初始化
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    temp_file.write("客户编号,发生金额,交易日期,银行错误信\nCUST001,10000,20240501,交易成功\n")
    temp_file.close()
    
    try:
        analyzer = RiskAnalyzer(temp_file.name)
        
        # 测试各种金额组合
        test_cases = [
            ([15000, 15000, 15000], True, "所有金额都≥10000且相等"),
            ([10000, 10000, 10000], True, "边界情况：所有金额都=10000"),
            ([15000, 20000, 18000], True, "所有金额都≥10000且在容差范围内"),
            ([9999, 15000, 15000], False, "有金额<10000"),
            ([5000, 8000, 6000], False, "所有金额都<10000"),
            ([10000, 15000, 25000], False, "金额差超过容差范围"),
            ([15000, 15000], False, "金额数量不足3个"),
        ]
        
        all_passed = True
        for amounts, expected, description in test_cases:
            result = analyzer._amounts_match(amounts)
            if result != expected:
                print(f"❌ 金额匹配测试失败: {amounts} - {description}")
                print(f"   期望: {expected}, 实际: {result}")
                all_passed = False
            else:
                print(f"✅ 金额匹配测试通过: {amounts} - {description}")
        
        return all_passed
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_edge_cases():
    """测试边界情况"""
    print("🧪 测试边界情况...")
    
    from merge_dbf import RiskAnalyzer
    
    # 创建边界情况测试数据
    test_data = {
        '客户编号': ['CUST001'] * 3 + ['CUST002'] * 3,
        '发生金额': [
            # CUST001: 边界情况 - 正好10000元
            '10000.00', '10000.00', '10000.00',
            
            # CUST002: 边界情况 - 9999.99元（应该被过滤）
            '9999.99', '9999.99', '9999.99',
        ],
        '交易日期': [
            '20240501', '20240602', '20240701',  # CUST001
            '20240501', '20240602', '20240701',  # CUST002
        ],
        '银行错误信': ['交易成功'] * 6
    }
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        analyzer = RiskAnalyzer(temp_file.name)
        analyzer._load_data()
        
        # 执行定期转账风险检测
        regular_risks = analyzer._check_regular_transfer_risk()
        
        # 分析检测结果
        detected_customers = set()
        for idx, pattern in regular_risks.items():
            customer_id = analyzer.data.loc[idx, '客户编号']
            detected_customers.add(customer_id)
        
        print(f"📋 边界情况检测到的客户: {detected_customers}")
        
        # 验证结果：只有CUST001应该被检测（10000元），CUST002不应该被检测（9999.99元）
        if 'CUST001' in detected_customers and 'CUST002' not in detected_customers:
            print("✅ 边界情况测试通过")
            return True
        else:
            print("❌ 边界情况测试失败")
            return False
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始定期转账误报减少测试...")
    print("=" * 60)
    
    tests = [
        test_regular_transfer_false_positive_reduction,
        test_amounts_match_logic,
        test_edge_cases
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print("✅ 测试通过\n")
            else:
                print("❌ 测试失败\n")
        except Exception as e:
            print(f"❌ 测试出错: {str(e)}\n")
    
    print("=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 定期转账误报减少测试全部通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
