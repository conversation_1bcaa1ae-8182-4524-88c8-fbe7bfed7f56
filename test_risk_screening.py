#!/usr/bin/env python3
"""
风险排查功能测试脚本
测试IP归属地查询、风险信息识别等功能
"""

import sys
import os
import tempfile
import pandas as pd
import unittest
from unittest.mock import patch, MagicMock

# 添加当前目录到路径
sys.path.insert(0, '.')

def test_ip_location_querier():
    """测试IP归属地查询功能"""
    print("🧪 测试1: IP归属地查询功能...")
    
    from merge_dbf import IPLocationQuerier
    
    # 创建测试数据
    test_data = {
        'IIP重复值': ['***********', "'********", '**********'],
        '客户编号': ['CUST001', 'CUST002', 'CUST003'],
        '客户姓名': ['张三', '李四', '王五']
    }
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        # 测试IP地址清理功能
        querier = IPLocationQuerier(temp_file.name)
        
        # 测试IP地址验证
        test_cases = [
            ('***********', True),
            ("'********", True),
            ('invalid_ip', False),
            ('', False)
        ]
        
        all_passed = True
        for ip_str, expected in test_cases:
            result = querier._is_valid_ip(ip_str)
            if result != expected:
                print(f"   ❌ IP验证失败: '{ip_str}' 期望 {expected}, 实际 {result}")
                all_passed = False
        
        if all_passed:
            print("   ✅ IP地址验证功能正常")
        
        # 测试IP地址清理
        clean_test_cases = [
            ("'***********", '***********'),
            ('********-test', '********'),
            ('invalid', '')
        ]
        
        for input_ip, expected in clean_test_cases:
            result = querier._clean_ip_address(input_ip)
            if result != expected:
                print(f"   ❌ IP清理失败: '{input_ip}' 期望 '{expected}', 实际 '{result}'")
                all_passed = False
        
        if all_passed:
            print("   ✅ IP地址清理功能正常")
        
        # 测试数据加载
        querier._load_data()
        if querier.data is not None and len(querier.data) > 0:
            print("   ✅ 数据加载功能正常")
        else:
            print("   ❌ 数据加载失败")
            all_passed = False
        
        # 测试IP提取
        querier._extract_unique_ips()
        if len(querier.unique_ips) > 0:
            print(f"   ✅ IP提取功能正常，提取到 {len(querier.unique_ips)} 个IP")
        else:
            print("   ❌ IP提取失败")
            all_passed = False
        
        return all_passed
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_risk_analyzer():
    """测试风险信息识别功能"""
    print("🧪 测试2: 风险信息识别功能...")
    
    from merge_dbf import RiskAnalyzer
    
    # 创建测试数据
    test_data = {
        '客户编号': ['CUST001', 'CUST002', 'CUST003', 'CUST004', 'CUST005'],
        '发生金额': ['10000', '15999', '8000', '20000', '12345'],
        '交易日期': ['20231215', '20231210', '20231205', '20231228', '20231101'],
        '银行错误信': ['交易成功', '密码错误', '余额不足', '交易成功 密码', '正常']
    }
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        analyzer = RiskAnalyzer(temp_file.name)
        analyzer._load_data()
        
        if analyzer.data is None:
            print("   ❌ 数据加载失败")
            return False
        
        print("   ✅ 数据加载功能正常")
        
        # 测试各种风险检查
        test_cases = [
            # 整数转账风险
            ({'发生金额': '10000'}, analyzer._check_integer_transfer_risk, True),
            ({'发生金额': '15999'}, analyzer._check_integer_transfer_risk, True),  # 包含999
            ({'发生金额': '8000'}, analyzer._check_integer_transfer_risk, False),
            
            # 发薪日交易风险
            ({'发生金额': '15000', '交易日期': '20231215'}, analyzer._check_payday_transaction_risk, True),
            ({'发生金额': '15000', '交易日期': '20231205'}, analyzer._check_payday_transaction_risk, False),
            ({'发生金额': '8000', '交易日期': '20231215'}, analyzer._check_payday_transaction_risk, False),
            
            # 月初月末交易风险
            ({'发生金额': '15000', '交易日期': '20231228'}, analyzer._check_month_end_transaction_risk, True),
            ({'发生金额': '15000', '交易日期': '20231101'}, analyzer._check_month_end_transaction_risk, True),
            ({'发生金额': '15000', '交易日期': '20231215'}, analyzer._check_month_end_transaction_risk, False),
        ]
        
        all_passed = True
        for row_data, check_func, expected in test_cases:
            result = check_func(row_data)
            if result != expected:
                print(f"   ❌ 风险检查失败: {row_data} 期望 {expected}, 实际 {result}")
                all_passed = False
        
        if all_passed:
            print("   ✅ 风险检查功能正常")
        
        # 测试完整分析流程
        analyzer._analyze_risks()
        
        # 检查是否添加了风险信息列
        if '风险信息' in analyzer.data.columns:
            print("   ✅ 风险信息列添加成功")
            
            # 检查是否识别到风险
            risk_records = analyzer.data[analyzer.data['风险信息'] != '']
            if len(risk_records) > 0:
                print(f"   ✅ 识别到 {len(risk_records)} 条风险记录")
            else:
                print("   ⚠️ 未识别到风险记录")
        else:
            print("   ❌ 风险信息列添加失败")
            all_passed = False
        
        return all_passed
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_risk_screening_processor():
    """测试风险排查处理器"""
    print("🧪 测试3: 风险排查处理器...")
    
    from merge_dbf import RiskScreeningProcessor
    
    # 创建IP同源数据测试文件
    ip_data = {
        'IIP重复值': ['***********', '********'],
        '客户编号': ['CUST001', 'CUST002'],
        '客户姓名': ['张三', '李四']
    }
    
    ip_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(ip_data).to_csv(ip_file.name, index=False)
    ip_file.close()
    
    # 创建风险分析测试文件
    risk_data = {
        '客户编号': ['CUST001', 'CUST002'],
        '发生金额': ['10000', '15999'],
        '交易日期': ['20231215', '20231210'],
        '银行错误信': ['交易成功', '密码错误']
    }
    
    risk_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(risk_data).to_csv(risk_file.name, index=False)
    risk_file.close()
    
    try:
        # 创建处理器
        processor = RiskScreeningProcessor(
            ip_file.name,
            risk_file.name
        )
        
        print("   ✅ 风险排查处理器创建成功")
        
        # 测试处理器属性
        if processor.ip_source_file == ip_file.name:
            print("   ✅ IP源文件路径设置正确")
        else:
            print("   ❌ IP源文件路径设置错误")
            return False
        
        if processor.risk_analysis_file == risk_file.name:
            print("   ✅ 风险分析文件路径设置正确")
        else:
            print("   ❌ 风险分析文件路径设置错误")
            return False
        
        return True
        
    finally:
        try:
            os.unlink(ip_file.name)
            os.unlink(risk_file.name)
        except:
            pass

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始风险排查功能测试...")
    print("=" * 60)
    
    tests = [
        test_ip_location_querier,
        test_risk_analyzer,
        test_risk_screening_processor
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print("✅ 测试通过\n")
            else:
                print("❌ 测试失败\n")
        except Exception as e:
            print(f"❌ 测试出错: {str(e)}\n")
    
    print("=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！风险排查功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
