#!/usr/bin/env python3
"""
测试IP列检测逻辑修复
验证各种IP格式的正确识别和解析
"""

import sys
import os
import tempfile
import pandas as pd

# 添加当前目录到路径
sys.path.insert(0, '.')

def test_ip_column_detection():
    """测试IP列检测逻辑"""
    print("🧪 测试IP列检测逻辑修复...")
    
    from merge_dbf import IPLocationQuerier
    
    # 创建包含各种IP格式的测试数据
    test_data = {
        'IIP重复值': [
            "'100127008112",  # 12位数字串
            "'2404:7A80:8A41:6000:1126:EF9C:ABDA:A6CD",  # IPv6
            "'240E:878:54:5E90:5CC0:981F:7DCF:4851",  # IPv6
            "'fe80::b251:3056:689b:a690",  # IPv6简化形式
            "*************",  # 标准IPv4
            "'***********",  # 带单引号的IPv4
            "invalid_data"  # 无效数据
        ],
        '客户编号': ['CUST001', 'CUST002', 'CUST003', 'CUST004', 'CUST005', 'CUST006', 'CUST007'],
        '客户姓名': ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九']
    }
    
    # 创建临时CSV文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    pd.DataFrame(test_data).to_csv(temp_file.name, index=False)
    temp_file.close()
    
    try:
        # 测试IP地址检测和解析
        querier = IPLocationQuerier(temp_file.name)
        querier._load_data()
        
        print(f"📋 数据文件列名: {list(querier.data.columns)}")
        
        # 执行IP提取
        querier._extract_unique_ips()
        
        # 验证结果
        expected_ips = {
            "*************",  # 从12位数字串解析
            "2404:7A80:8A41:6000:1126:EF9C:ABDA:A6CD",  # IPv6
            "240E:878:54:5E90:5CC0:981F:7DCF:4851",  # IPv6
            "fe80::b251:3056:689b:a690",  # IPv6简化
            "*************",  # IPv4
            "***********"  # IPv4
        }
        
        print(f"✅ 提取到的IP地址: {querier.unique_ips}")
        print(f"📊 期望的IP地址: {expected_ips}")
        
        # 检查是否正确提取了所有有效IP
        missing_ips = expected_ips - querier.unique_ips
        extra_ips = querier.unique_ips - expected_ips
        
        success = True
        if missing_ips:
            print(f"❌ 缺失的IP地址: {missing_ips}")
            success = False
        
        if extra_ips:
            print(f"⚠️ 额外的IP地址: {extra_ips}")
        
        if len(querier.unique_ips) >= 4:  # 至少应该提取到4个有效IP
            print(f"✅ IP提取成功，共提取到 {len(querier.unique_ips)} 个有效IP")
        else:
            print(f"❌ IP提取不足，仅提取到 {len(querier.unique_ips)} 个IP")
            success = False
        
        return success
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_ip_parsing_formats():
    """测试各种IP格式的解析"""
    print("🧪 测试IP格式解析...")
    
    from merge_dbf import IPLocationQuerier
    
    # 创建临时文件用于初始化
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    temp_file.write("IIP重复值\n***********\n")
    temp_file.close()
    
    try:
        querier = IPLocationQuerier(temp_file.name)
        
        # 测试各种IP格式
        test_cases = [
            ("'100127008112", "*************"),  # 12位数字串
            ("'2404:7A80:8A41:6000:1126:EF9C:ABDA:A6CD", "2404:7A80:8A41:6000:1126:EF9C:ABDA:A6CD"),  # IPv6
            ("'240E:878:54:5E90:5CC0:981F:7DCF:4851", "240E:878:54:5E90:5CC0:981F:7DCF:4851"),  # IPv6
            ("'fe80::b251:3056:689b:a690", "fe80::b251:3056:689b:a690"),  # IPv6简化
            ("*************", "*************"),  # 标准IPv4
            ("'***********", "***********"),  # 带单引号IPv4
            ("invalid_data", ""),  # 无效数据
            ("", ""),  # 空字符串
        ]
        
        all_passed = True
        for input_ip, expected in test_cases:
            result = querier._clean_ip_address(input_ip)
            if result != expected:
                print(f"❌ IP解析失败: '{input_ip}' 期望 '{expected}', 实际 '{result}'")
                all_passed = False
            else:
                print(f"✅ IP解析正确: '{input_ip}' -> '{result}'")
        
        return all_passed
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def test_ip_validation():
    """测试IP地址验证"""
    print("🧪 测试IP地址验证...")
    
    from merge_dbf import IPLocationQuerier
    
    # 创建临时文件用于初始化
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    temp_file.write("IIP重复值\n***********\n")
    temp_file.close()
    
    try:
        querier = IPLocationQuerier(temp_file.name)
        
        # 测试IP验证
        test_cases = [
            ("***********", True),  # 有效IPv4
            ("*************", True),  # 有效IPv4
            ("2404:7A80:8A41:6000:1126:EF9C:ABDA:A6CD", True),  # 有效IPv6
            ("fe80::b251:3056:689b:a690", True),  # 有效IPv6简化
            ("256.1.1.1", False),  # 无效IPv4
            ("192.168.1", False),  # 不完整IPv4
            ("invalid", False),  # 无效字符串
            ("", False),  # 空字符串
        ]
        
        all_passed = True
        for ip, expected in test_cases:
            result = querier._is_valid_ip(ip)
            if result != expected:
                print(f"❌ IP验证失败: '{ip}' 期望 {expected}, 实际 {result}")
                all_passed = False
            else:
                print(f"✅ IP验证正确: '{ip}' -> {result}")
        
        return all_passed
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始IP列检测逻辑修复测试...")
    print("=" * 60)
    
    tests = [
        test_ip_column_detection,
        test_ip_parsing_formats,
        test_ip_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print("✅ 测试通过\n")
            else:
                print("❌ 测试失败\n")
        except Exception as e:
            print(f"❌ 测试出错: {str(e)}\n")
    
    print("=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 IP列检测逻辑修复测试全部通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
