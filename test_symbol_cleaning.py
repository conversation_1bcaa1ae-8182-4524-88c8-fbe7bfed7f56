#!/usr/bin/env python3
"""
测试符号清理逻辑的脚本
验证 _clean_value_for_comparison 方法是否正确忽略所有符号
"""

import sys
import os
sys.path.insert(0, '.')

def test_symbol_cleaning():
    """测试符号清理功能"""
    print("🧪 测试符号清理逻辑...")
    
    from merge_dbf import SameSourceDataAnalyzer
    import tempfile
    import pandas as pd
    
    # 创建临时文件用于初始化分析器
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')
    temp_file.write("客户编号,MAC\nCUST001,AA:BB:CC:DD:EE:FF\n")
    temp_file.close()
    
    try:
        analyzer = SameSourceDataAnalyzer(temp_file.name)
        
        # 测试用例：验证符号完全被忽略
        test_cases = [
            # 基本符号测试
            ("************", "101002021", True),  # IP地址格式
            ("'18511112222", "18511112222", True),  # 前导引号
            ("AA:BB:CC:DD:EE:FF", "AABBCCDDEEFF", True),  # MAC地址格式
            ("123-456-789", "123456789", True),  # 连字符
            ("(123)456-7890", "1234567890", True),  # 电话号码格式
            ("ID_12345", "ID12345", True),  # 下划线
            ("<EMAIL>", "TESTDOMAINCOM", True),  # 邮箱格式
            ("***********", "19216811", True),  # 另一个IP
            ("MAC-AA-BB-CC", "MACAABBCC", True),  # MAC变体
            
            # 不同值测试
            ("123456", "654321", False),  # 完全不同的数字
            ("ABCDEF", "FEDCBA", False),  # 完全不同的字母
            
            # 特殊字符测试
            ("test!@#$%^&*()", "TEST", True),  # 各种特殊符号
            ("a.b-c_d:e;f|g\\h/i", "ABCDEFGHI", True),  # 混合符号
            ("123.456.789.0", "1234567890", True),  # 多个点
            ("[test]", "TEST", True),  # 方括号
            ("{test}", "TEST", True),  # 花括号
            ("test<>", "TEST", True),  # 尖括号
            ("test+test", "TESTTEST", True),  # 加号
            ("test=test", "TESTTEST", True),  # 等号
            ("test~test", "TESTTEST", True),  # 波浪号
            ("test`test", "TESTTEST", True),  # 反引号
        ]
        
        print("   测试符号清理结果:")
        all_passed = True
        
        for input1, input2, should_be_equal in test_cases:
            clean1 = analyzer._clean_value_for_comparison(input1)
            clean2 = analyzer._clean_value_for_comparison(input2)
            is_equal = clean1 == clean2
            
            if is_equal == should_be_equal:
                status = "✅"
            else:
                status = "❌"
                all_passed = False
            
            print(f"   {status} '{input1}' vs '{input2}'")
            print(f"      清理后: '{clean1}' vs '{clean2}' -> 相等: {is_equal} (期望: {should_be_equal})")
        
        # 额外测试：确保中文字符被保留
        chinese_test_cases = [
            ("测试-数据", "测试数据", True),
            ("用户@123", "用户123", True),
            ("北京.上海", "北京上海", True),
        ]
        
        print("\n   测试中文字符处理:")
        for input1, input2, should_be_equal in chinese_test_cases:
            clean1 = analyzer._clean_value_for_comparison(input1)
            clean2 = analyzer._clean_value_for_comparison(input2)
            is_equal = clean1 == clean2
            
            if is_equal == should_be_equal:
                status = "✅"
            else:
                status = "❌"
                all_passed = False
            
            print(f"   {status} '{input1}' vs '{input2}'")
            print(f"      清理后: '{clean1}' vs '{clean2}' -> 相等: {is_equal}")
        
        if all_passed:
            print("\n✅ 符号清理逻辑测试全部通过！")
            print("   确认：_clean_value_for_comparison() 方法正确忽略所有符号")
        else:
            print("\n❌ 符号清理逻辑测试失败！")
            print("   需要检查 _clean_value_for_comparison() 方法")
        
        return all_passed
        
    finally:
        try:
            os.unlink(temp_file.name)
        except:
            pass

def main():
    """主测试函数"""
    print("🚀 开始测试符号清理逻辑")
    print("=" * 60)
    
    try:
        result = test_symbol_cleaning()
        
        print("=" * 60)
        if result:
            print("🎉 符号清理逻辑验证成功！")
            print("   ✅ 所有符号都被正确忽略")
            print("   ✅ 中文字符被正确保留")
            print("   ✅ 重复值比较逻辑正确")
        else:
            print("⚠️ 符号清理逻辑需要修复！")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")

if __name__ == "__main__":
    main()
